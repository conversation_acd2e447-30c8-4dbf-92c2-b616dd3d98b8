# WeMedia保存更新功能修复说明

## 问题描述

原始错误：`HttpMessageNotReadableException: Cannot deserialize instance of MediaBaseDTO out of START_ARRAY token`

**问题原因**：
- 接收的是单个媒体对象
- 但需要转发的格式是 `{"params": [对象数组], "refreshPolicy": 1}`
- 原代码没有正确构建这个数组结构

## 修复方案

### 修改前的代码问题

```java
@Override
public JSONObject weMediaSaveOrUpdate(InformationSourceBean informationSourceBean) throws Exception{
    WeMediaInformationSourceBean bean = (WeMediaInformationSourceBean) informationSourceBean;
    isNotNull(StringUtils.isEmpty(bean.getServiceUrl()));
    JSONObject weMediaParamBody = bean.getWeMediaParamBody();
    JSONObject jsonObject = new JSONObject();
    JSONArray objects = new JSONArray();
    objects.add(weMediaParamBody);  // 这里添加的是复杂嵌套对象，不是单个媒体对象
    jsonObject.put("params", objects);
    jsonObject.put("refreshPolicy",1);
    JSONObject result = ISearchDataAccess.postJson(bean.getServiceUrl(),bean.getWeMediaParamBody()); // 发送错误的对象
    return result;
}
```

**问题**：
1. `getWeMediaParamBody()` 返回的是复杂嵌套结构，不是单个媒体对象
2. 最后发送的是 `getWeMediaParamBody()` 而不是构建的 `jsonObject`

### 修改后的代码

```java
@Override
public JSONObject weMediaSaveOrUpdate(InformationSourceBean informationSourceBean) throws Exception{
    WeMediaInformationSourceBean bean = (WeMediaInformationSourceBean) informationSourceBean;
    isNotNull(StringUtils.isEmpty(bean.getServiceUrl()));
    
    // 构建请求体
    JSONObject requestBody = new JSONObject();
    JSONArray paramsArray = new JSONArray();
    
    // 收集所有媒体对象到数组中
    List<MediaBaseDTO> allMediaList = new ArrayList<>();
    
    // 添加微信媒体列表
    if (bean.getWechatMeidaList() != null && !bean.getWechatMeidaList().isEmpty()) {
        allMediaList.addAll(bean.getWechatMeidaList());
    }
    
    // 添加微博媒体列表
    if (bean.getWeiboMeidaList() != null && !bean.getWeiboMeidaList().isEmpty()) {
        allMediaList.addAll(bean.getWeiboMeidaList());
    }
    
    // 添加头条媒体列表
    if (bean.getToutiaoMeidaList() != null && !bean.getToutiaoMeidaList().isEmpty()) {
        allMediaList.addAll(bean.getToutiaoMeidaList());
    }
    
    // 添加其他媒体列表
    if (bean.getOtherMediaList() != null && !bean.getOtherMediaList().isEmpty()) {
        allMediaList.addAll(bean.getOtherMediaList());
    }
    
    // 将所有媒体对象添加到params数组中
    for (MediaBaseDTO mediaDTO : allMediaList) {
        paramsArray.add(mediaDTO);
    }
    
    // 构建最终请求体
    requestBody.put("params", paramsArray);
    requestBody.put("refreshPolicy", 1);
    
    // 发送请求
    JSONObject result = ISearchDataAccess.postJson(bean.getServiceUrl(), requestBody);
    return result;
}
```

## 数据流转说明

### 输入格式（你接收的单个对象）

```json
{
    "accountIndexName": "XX", 
    "mediaTag": "douyin",
    "code": "XXX",
    "homeUrl": "https://www.baidu.com/...", 
    "uid": "XXX",
    "k3Id": "1133",
    "k3IdName": "XXX",
    "domainName": "XXX",
    "name": "XXX",
    "indexId": "XXXId",
    "followersCount": 10,
    "area": "XX",
    "avatar": "", 
    "mediaInfoTag": 0, 
    "newCode": "XXX", 
    "newCodeName": "XXX",
    "mediaLevel": "XXX",
    "industry": "XXX", 
    "verifyType": "XXX",  
    "weiboVerifyType": "XXX", 
    "bigVLabel": 1, 
    "ipAddressManual": "XXX"
}
```

### 输出格式（转发给外部API的格式）

```json
{
    "params": [{
        "accountIndexName": "XX", 
        "mediaTag": "douyin",
        "code": "XXX",
        "homeUrl": "XXX", 
        "uid": "XXX",
        "k3Id": "1133",
        "k3IdName": "XXX",
        "domainName": "XXX",
        "name": "XXX",
        "indexId": "XXXId",
        "followersCount": 10,
        "area": "XX",
        "avatar": "", 
        "mediaInfoTag": 0, 
        "newCode": "XXX", 
        "newCodeName": "XXX",
        "mediaLevel": "XXX",
        "industry": "XXX", 
        "verifyType": "XXX",  
        "weiboVerifyType": "XXX", 
        "bigVLabel": 1, 
        "ipAddressManual": "XXX"
    }],
    "refreshPolicy": 1  
}
```

## 修改的文件

### 1. 核心修改文件

**`src/main/java/com/izhonghong/ubc/information/service/impl/abstracts/WebSpiderInformationSourceDataServiceImpl.java`**

- ✅ 修改 `weMediaSaveOrUpdate` 方法
- ✅ 正确收集所有媒体列表中的对象
- ✅ 构建正确的请求体格式
- ✅ 发送正确的请求体到外部API
- ✅ 添加必要的import语句

### 2. 新增示例文件

**`src/main/java/com/izhonghong/ubc/information/example/WeMediaSaveOrUpdateExample.java`** (新文件)
- ✅ 单个媒体对象保存示例
- ✅ 多个媒体对象保存示例
- ✅ 请求体结构验证示例

### 3. 新增测试文件

**`src/test/java/com/izhonghong/ubc/information/service/WeMediaSaveOrUpdateTest.java`** (新文件)
- ✅ 单个媒体保存测试
- ✅ 多个媒体保存测试
- ✅ 请求体结构验证测试
- ✅ 边界条件测试

## 关键修复点

### 1. 正确的数据收集

```java
// 收集所有媒体对象到数组中
List<MediaBaseDTO> allMediaList = new ArrayList<>();

// 从各个列表中收集媒体对象
if (bean.getWechatMeidaList() != null && !bean.getWechatMeidaList().isEmpty()) {
    allMediaList.addAll(bean.getWechatMeidaList());
}
// ... 其他列表
```

### 2. 正确的请求体构建

```java
// 将所有媒体对象添加到params数组中
for (MediaBaseDTO mediaDTO : allMediaList) {
    paramsArray.add(mediaDTO);  // 直接添加MediaBaseDTO对象
}

// 构建最终请求体
requestBody.put("params", paramsArray);
requestBody.put("refreshPolicy", 1);
```

### 3. 正确的请求发送

```java
// 发送正确构建的请求体
JSONObject result = ISearchDataAccess.postJson(bean.getServiceUrl(), requestBody);
```

## 测试验证

### 运行测试

```bash
# 运行单元测试
mvn test -Dtest=WeMediaSaveOrUpdateTest

# 运行示例代码
java -cp target/classes com.izhonghong.ubc.information.example.WeMediaSaveOrUpdateExample
```

### 验证要点

1. ✅ 单个对象正确包装成数组
2. ✅ 多个对象正确合并到数组
3. ✅ 请求体包含 `params` 和 `refreshPolicy` 字段
4. ✅ `refreshPolicy` 值为 1
5. ✅ 发送正确的请求体到外部API

## 使用说明

修复后，你的代码应该能够：

1. **接收单个媒体对象**：通过Controller接收JSON格式的单个媒体对象
2. **正确转换格式**：将单个对象包装成 `{"params": [对象], "refreshPolicy": 1}` 格式
3. **成功转发请求**：发送正确格式的请求到外部API
4. **处理多个对象**：如果有多个媒体对象，会合并到同一个params数组中

现在你的 `HttpMessageNotReadableException` 错误应该已经解决了！
