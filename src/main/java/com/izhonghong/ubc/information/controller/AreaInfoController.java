package com.izhonghong.ubc.information.controller;


import java.util.List;

import com.izhonghong.ubc.information.entity.vo.AreaInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.aop.OperateLog;
import com.izhonghong.ubc.information.constant.BusinessConstant;
import com.izhonghong.ubc.information.entity.AreaInfo;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.result.StatusCode;
import com.izhonghong.ubc.information.service.AreaInfoService;
import com.izhonghong.ubc.information.util.StringUtil;
import com.izhonghong.ubc.information.util.TreeUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @since 2022-03-18
 */
@Api(value = "行政区域")
@RestController
@RequestMapping("/areaInfo")
public class AreaInfoController {

    @Autowired
    private AreaInfoService areaInfoService;

    @ApiOperation(value = "行政区域树", notes = "行政区域树")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "行政区域树")
    @GetMapping("selectAreaTree")
    public Result selectAreaTree(String root, Integer type, String name) {

        if (StringUtils.isEmpty(root)) {
            root = "-1";
        }

        if (StringUtils.isEmpty(type)) {
            type = 0;
        }

        List<AreaInfo> list = areaInfoService.selectAreaInfoList(root, null, name);
        if (!StringUtil.isListNull(list)) {
            list = (List<AreaInfo>) TreeUtil.toTree(list, root);
        }
        return Result.successResult().setData(list);

    }

    @ApiOperation(value = "查询地区code", notes = "查询行政区域code")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "查询行政区域code")
    @GetMapping("selectAreaCode")
    public Result selectAreaCode(@RequestParam String area) {
        if (StringUtils.isEmpty(area)) {
            return Result.fail(StatusCode.SYS_COMMON_INVALID_PARAM);
        }

        JSONObject list = areaInfoService.selectAreaCode(area);
        return Result.successResult().setData(list);

    }

    @ApiOperation(value = "国内行政区域树")
    @GetMapping("selectInlandTree")
    public Result selectInlandTree() {
        List<AreaInfoVo> list = areaInfoService.selectInlandTree();
        return Result.successResult().setData(list);
    }

//    @ApiOperation(value = "国内行政区域树（过滤市级以下）", notes = "国内行政区域树（过滤市级以下）")
//    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "国内行政区域树（过滤市级以下）")
//    @GetMapping("selectInlandTreeNew")
//    public Result selectInlandTreeNew() {
//        List<AreaInfoVo> list = areaInfoService.selectInlandTreeNew();
//        return Result.successResult().setData(list);
//    }

}
