package com.izhonghong.ubc.information.controller;


import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.izhonghong.ubc.information.common.enums.WebSpiderDataUrlEnum;
import com.izhonghong.ubc.information.common.enums.WebSpiderNewDataUrlEnum;
import com.izhonghong.ubc.information.constant.CommonConstant;
import com.izhonghong.ubc.information.entity.vo.MediaDetailVO;
import com.izhonghong.ubc.information.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.aop.OperateLog;
import com.izhonghong.ubc.information.common.enums.SourceStatusEnum;
import com.izhonghong.ubc.information.config.WebCrawlerConfig;
import com.izhonghong.ubc.information.constant.BusinessConstant;
import com.izhonghong.ubc.information.elasticsearch.ElasticSearchAPI;
import com.izhonghong.ubc.information.entity.MediaLibraryInformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.entity.dto.MediaLibraryDTO;
import com.izhonghong.ubc.information.entity.personal.User;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.result.StatusCode;
import com.izhonghong.ubc.information.service.AreaInfoService;
import com.izhonghong.ubc.information.service.MediaService;

import cn.cnhon.util.MD5;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Slf4j
@Api(tags="MediaLibrary", value="媒体库")
@RestController
@RequestMapping("/mediaLibrary")
public class MediaLibraryController extends BaseController{

	@Autowired
	private ElasticSearchAPI elasticSearchApi;

	@Autowired
	private WebCrawlerConfig webCrawlerConfig;

	@Autowired
	private ExportUtils exportUtils;

	@Autowired
	private MediaService mediaService;

	@Autowired
	private AreaInfoService areaInfoService;

	/**
	    * 添加
    */
	@ApiOperation(value = "媒体库的修改或新增", notes = "媒体库的修改或新增")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA_LIBRARY, operateType = BusinessConstant.OPERATE_TYPE_UPDATE, operateDescription = "媒体词库的修改或新增")
    @PostMapping("saveOrUpdate")
    public Result saveOrUpdate(@RequestBody MediaBaseDTO mediaLibraryDTO){
		if (StringUtils.isEmpty(mediaLibraryDTO.getId())
				&&(StringUtils.isEmpty(mediaLibraryDTO.getName())
				||StringUtils.isEmpty(mediaLibraryDTO.getNew_code())
				||StringUtils.isEmpty(mediaLibraryDTO.getWeb_url())
				)) {
			return Result.fail(StatusCode.SYS_COMMON_INVALID_PARAM);
		}

		if(StringUtils.isEmpty(mediaLibraryDTO.getArea()) || !StringUtils.isEmpty(mediaLibraryDTO.getArea()) && !areaInfoService.checkAreaName(mediaLibraryDTO.getArea())) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM,"区域参数不合法");
		}

		if(!StringUtil.isHttpUrl(mediaLibraryDTO.getWeb_url())
				||!StringUtil.checkSourceName(mediaLibraryDTO.getName())
				||(!StringUtils.isEmpty(mediaLibraryDTO.getOrganizer()) && !StringUtil.checkSourceName(mediaLibraryDTO.getOrganizer()) )
				|| mediaLibraryDTO.getName().length() > 30
				|| mediaLibraryDTO.getOrganizer().length() > 30){
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM);
		}

		MediaLibraryInformationSourceBean  mediaLibraryInformationSourceBean = new MediaLibraryInformationSourceBean();
		if(!StringUtils.isEmpty(mediaLibraryDTO.getId())) {
			mediaLibraryInformationSourceBean.setRequestType(1);
		}
		String uid = MD5.encode(mediaLibraryDTO.getWeb_url());
		saveOperationLog("网站模块的新增修改", uid, mediaLibraryDTO.toString());
		mediaLibraryInformationSourceBean.setMediaLibraryList(mediaLibraryDTO);
    	JSONObject result = elasticSearchApi.saveOrUpdate(mediaLibraryInformationSourceBean);

		return Result.successResult().setData(result);

    }



	@ApiOperation(value = "媒体库的批量修改", notes = "媒体库的批量修改")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA_LIBRARY, operateType = BusinessConstant.OPERATE_TYPE_UPDATE, operateDescription = "媒体库的批量修改")
    @PostMapping("updateBatch")
    public Result updateBatch(@RequestBody List<MediaBaseDTO> mediaList){

		long idCount = mediaList.stream().filter(t->t.getId() == null).count();
		 Map<String,String> mediaType = mediaService.mediaTypeMap();
		if(idCount > 0) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM, "主键不能为空");
		}
		long checkCount = mediaList.stream().filter(t->!StringUtils.isEmpty(t.getOrganizer()) && !StringUtil.checkSourceName(t.getOrganizer())).count();
		if(checkCount > 0) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM, "单位名称不合法");
		}
		long checkAreaCount = mediaList.stream().filter(t->!StringUtils.isEmpty(t.getArea()) && !areaInfoService.checkAreaName(t.getArea())).count();
		if(checkAreaCount > 0) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM, "区域名称异常");
		}
		MediaLibraryInformationSourceBean  mediaLibraryInformationSourceBean = new MediaLibraryInformationSourceBean();
		mediaList.forEach(t->{
			t.setMedia_type(mediaType.get(t.getCode()));
			if(StringUtils.isEmpty(t.getArea())) {
				t.setArea(null);
			}else {
				t.setArea(mediaLibraryInformationSourceBean.cleanArea(t.getArea()));
			}
			if(StringUtils.isEmpty(t.getCode())) {
				t.setCode(null);
			}
			if(StringUtils.isEmpty(t.getOrganizer())) {
				t.setOrganizer(null);
			}
			t.setUid(null);
			String uid = MD5.encode(t.getWeb_url());
			saveOperationLog("网站模块的批量修改", uid, t.toString());
		});

		mediaLibraryInformationSourceBean.setRequestType(1);
		mediaLibraryInformationSourceBean.setMeidaList(mediaList);
    	JSONObject result = elasticSearchApi.saveOrUpdate(mediaLibraryInformationSourceBean);
		return Result.successResult().setData(result);
    }

	@ApiOperation(value = "媒体列表的导入", notes = "媒体列表的导入")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA_LIBRARY, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的导入")
	@PostMapping("uploadMedia")
	 public Result uploadMedia(@RequestParam("files") MultipartFile files , HttpServletRequest request, HttpServletResponse response){

		    User user = getLoginUser();
			String name=files.getOriginalFilename();
			//不是以
			if(!name.endsWith(".xlsx")&&!name.endsWith(".xls")) {
				return Result.fail(StatusCode.SYS_COMMON_EXCEL_IMPORT_ERR,"文件格式不对,请上传xlsx/xls格式文件");
			}
			JSONObject json = new JSONObject();
			try {
				 json = mediaService.importUpdateBatch(name, files.getInputStream(), user,1);
				 if(json.getBoolean("flage")) {
					 return Result.successResult();
				 }
			} catch (IOException e) {
				e.printStackTrace();
			}
			return Result.fail(StatusCode.SYS_COMMON_EXCEL_IMPORT_ERR, json.toJSONString());

	}


	@ApiOperation(value = "媒体列表的分页查询", notes = "媒体列表的分页查询")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA_LIBRARY, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的分页查询")
	@PostMapping("selectPage")
	public Result selectPage(@RequestBody MediaLibraryDTO mediaLibraryDTO){

		    if(!StringUtils.isEmpty(mediaLibraryDTO.getCode())) {
		    	mediaLibraryDTO.setMedia_type(mediaLibraryDTO.getCode());
		    }
		    Integer page =  mediaLibraryDTO.getPageNo() == null ? 1 : mediaLibraryDTO.getPageNo();
			Integer size =  mediaLibraryDTO.getSize() == null ? 20 :mediaLibraryDTO.getSize();
			mediaLibraryDTO.setPageNo(page);
			mediaLibraryDTO.setSize(size);

	 	   JSONObject result = elasticSearchApi.search(mediaLibraryDTO);

			return Result.successResult().setData(result);

	 }

	 //根据关键词获取所有主办单位
	@ApiOperation(value = "主办单位前缀匹配查询", notes = "主办单位前缀匹配查询")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA_LIBRARY, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "主办单位前缀匹配查询")
	@PostMapping("selectOrganization")
	public Result selectOrganization(@RequestBody MediaLibraryDTO mediaLibraryDTO){
		Assert.require(!StringUtil.isEmpty(mediaLibraryDTO.getName()), "主办单位名称不能为空");
		String url = webCrawlerConfig.getServerUrl() + WebSpiderNewDataUrlEnum.ES_ORGANIZATION_SEARCH.getUrl() + mediaLibraryDTO.getName();
		JSONArray result = new JSONArray();
		try {
			String resultString = new HttpRequest(url).get().executeAsString();
			log.info("==selectOrganization resultString=={}", resultString);
			if (!StringUtil.isEmpty(resultString)) {
				result = JSON.parseArray(resultString);
			}
		} catch (Exception e) {
			log.error("==selectOrganization error==", e);
		}
		return Result.successResult().setData(result);
	}

	//根据k3_id查询信息源详情
	@ApiOperation(value = "获取单个信息源详情", notes = "获取单个信息源详情")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_MODULE_MEDIA, operateDescription = "获取单个信息源详情")
	@GetMapping("mediaDetail")
	public Result mediaDetail(@RequestParam("k3_id") String k3Id) {
		Assert.require(!StringUtil.isEmpty(k3Id), "主k3_id不能为空");
		String url = webCrawlerConfig.getServerUrl() + WebSpiderNewDataUrlEnum.ES_SEARCH_MID.getUrl() + k3Id;
		Object result = null;
		try {
			String resultString = new HttpRequest(url).get().executeAsString();
			log.info("==mediaDetail resultString=={}", resultString);
			if (!StringUtil.isEmpty(resultString)) {
				result = JSON.parseArray(resultString);
			}
		} catch (Exception e) {
			log.error("==mediaDetail error==", e);
		}
		return Result.successResult().setData(result);
	}
	  /**
	    * 导入
   */
	@ApiOperation(value = "媒体列表的导出", notes = "媒体列表的导出")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA_LIBRARY, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的导出")
	@PostMapping("exportMedia")
    public Result exportMedia(@RequestBody MediaLibraryDTO mediaLibraryDTO, HttpServletRequest request, HttpServletResponse response){
	    Integer page =  mediaLibraryDTO.getPageNo() == null ? 1 : mediaLibraryDTO.getPageNo();
	    if(!StringUtils.isEmpty(mediaLibraryDTO.getMedia_type())) {
	    	mediaLibraryDTO.setCode(mediaLibraryDTO.getMedia_type());
	    }
//		Integer size =  mediaLibraryDTO.getSize() == null ? 20 :mediaLibraryDTO.getSize();
		mediaLibraryDTO.setPageNo(page);
		mediaLibraryDTO.setSize(3000);

	    JSONObject result = elasticSearchApi.search(mediaLibraryDTO);
	    if(result == null) {
	    	return Result.fail(StatusCode.SYS_COMMON_EXCEL_EXPORT_ERR);
	    }
	    JSONArray dataArray = result.getJSONArray("data");
	    if(dataArray == null || dataArray.size() < 1) {
	    	return Result.fail(StatusCode.SYS_COMMON_EXCEL_EXPORT_DATA_ERR);
	    }
	    Map<Integer,String> statusMap = SourceStatusEnum.getMap();
	    Map<String,String> mediaType = mediaService.mediaTypeMap();
	    User user =getLoginUser();
	    String fileName = "网站管理列表导出_"+user.getUsername() + "_" + DateUtil.getCurrDate();
	    String[] sheets = new String[] {"主办单位","网站名称","首页链接","所属区域","添加时间","媒体类型","采集状态","备注"};
	    String[][] values = new String[dataArray.size()][sheets.length];
	    for (int i = 0; i < dataArray.size(); i++) {
			JSONObject dataJson = dataArray.getJSONObject(i);
			values[i][0] = dataJson.getString("organizer");
			values[i][1] = dataJson.getString("name");
			values[i][2] = dataJson.getString("web_url");
			values[i][3] = dataJson.getString("area");
			values[i][4] = dataJson.get("create_time") ==null ? "":DateUtil.longToTimeStr(dataJson.getLong("create_time"));
			String medias= mediaType.get(dataJson.getString("code"));
			values[i][5] =  medias== null ? "":medias;
			values[i][6] = statusMap.get(dataJson.getInteger("status"));
			values[i][7] = dataJson.getString("remarks");

		}
	    String title ="网站管理信息列表";
	    if("".equals(mediaLibraryDTO.getSourceType())) {
	    	title ="自媒体信息列表";
	    }
	    ExcelExportUtils.createWorkbook(fileName, title,sheets, values, response, request);

		return Result.successResult();

    }

	@ApiOperation(value = "媒体库模板导出", notes = "媒体库模板导出")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA_LIBRARY, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体库模板导出")
	@GetMapping("exportSampleFile")
    public void exportSampleFile(HttpServletRequest request, HttpServletResponse response){

		    request.getSession();
	        String rootPath = webCrawlerConfig.getFileBasedir();//存储文件的目录
	        String fullPath = rootPath + "//medialibrary.xlsx";//文件的位置
		    Boolean flag =  exportUtils.exportDocument(fullPath,"网站管理账号导入模板.xlsx",response);
    }

	@ApiOperation(value = "媒体库删除", notes = "媒体库删除")
	@PostMapping("delete")
    public Result delete(@RequestBody List<String> ids) {
		if (CollUtil.isEmpty(ids)) {
			return Result.fail(StatusCode.SYS_COMMON_INVALID_PARAM);
		}
		List<MediaBaseDTO> mediaBaseList = ids.stream().map(id -> {
			MediaBaseDTO mediaBaseDTO = new MediaBaseDTO();
			mediaBaseDTO.setId(id);
			return mediaBaseDTO;
		}).collect(Collectors.toList());

		MediaLibraryInformationSourceBean  mediaLibraryInformationSourceBean = new MediaLibraryInformationSourceBean();
		mediaLibraryInformationSourceBean.setMediaLibraryList(mediaBaseList);
		JSONObject result = elasticSearchApi.delete(mediaLibraryInformationSourceBean);
		return Result.successResult().setData(result);
	}
}
