package com.izhonghong.ubc.information.entity.dto;

import com.izhonghong.ubc.information.common.enums.SourceTypeMenu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *      媒体库
 * **/
@Data
public class WeMediaDTO extends MediaBaseDTO{
	
	/**
	 * @see SourceTypeMenu
	 * **/
	@ApiModelProperty(value="客户类型值中文：微博、微信、头天")
	private String sourceType;
	
	@ApiModelProperty(value="媒体名称")
	private String name;
	
	@ApiModelProperty(value = "媒体主页")
	private String web_url;
		
	@ApiModelProperty(value = "媒体性质")
	private String media;
	
	@ApiModelProperty(value = "账号级别：影响采集频率")
	private String accountLevel;
	
	
	@ApiModelProperty(value = "媒体分类")
	private String code;
	
	@ApiModelProperty(value = "媒体类型：微博微信网站等一级分类")
	private String media_type;
	
	@ApiModelProperty(value = "具体的地方，比如 广东省:深圳市:南山区")
	private String area;
	
	@ApiModelProperty(value = "认证信息")
	private String verifiedInfo;
	

	@ApiModelProperty(name="页码")
	private Integer pageNo;
	
	@ApiModelProperty(name="每页条数")
	private Integer size;
	
	@ApiModelProperty(name="城市")
	private String city;

	@ApiModelProperty(name="信息源ID")
	private Integer k3Id;

	@ApiModelProperty(name="XX")
	private String mediaTag;

	@ApiModelProperty(name="XX信息源名字")
	private String k3IdName;

	@ApiModelProperty(name="微博认证类型 金V、橙V、黄V")
	private String weiboVerifyType;

	@ApiModelProperty(name="账号认证类型 政务、机构、企业、个人、未认证")
	private String verifyType;

	@ApiModelProperty(name="粉丝数范围起始值")
	private String followersCountRangeFrom;

	@ApiModelProperty(name="粉丝数范围结束值")
	private String followersCountRangeTo;

	@ApiModelProperty(name="大V标识,大V标签 0 否 1是")
	private String bigVLabel;

	@ApiModelProperty(name="IP属地")
	private Integer ipLocation;

	@ApiModelProperty(name="分页信息")
	private PaginatorDTO paginator;

	@ApiModelProperty(name="起始值")
	private String from;

	@ApiModelProperty(name="排序规则数组")
	private String sorts;

	@ApiModelProperty(name="")
	private String field;

	@ApiModelProperty(name="排序方式")
	private String order;

	public PaginatorDTO getPaginator() {
		return paginator;
	}

	public void setPaginator(PaginatorDTO paginator) {
		this.paginator = paginator;
		}

	public class PaginatorDTO {
		private Integer page;
		private Integer size;
		private Integer total;

		// getter和setter方法
	}

	}


