package com.izhonghong.ubc.information.example;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;

import java.util.Arrays;

/**
 * WeMedia保存或更新示例
 * 展示如何正确构建请求体
 * 
 * <AUTHOR>
 */
public class WeMediaSaveOrUpdateExample {

    /**
     * 示例1：单个媒体对象的保存
     */
    public void singleMediaExample() {
        // 创建媒体对象
        MediaBaseDTO mediaDTO = new MediaBaseDTO();
        mediaDTO.setAccountIndexName("XX");
        mediaDTO.setMediaTag("douyin");
        mediaDTO.setCode("XXX");
        mediaDTO.setHomeUrl("https://www.baidu.com/s?ie=utf-8&f=8&rsv_bp=1&rsv_idx=2&tn=baiduhome_pg&wd=%E5%BE%AE%E5%8D%9A&rsv_spt=1&oq=wps%25E5%25A6%2582%25E4%25BD%2595%25E6%2598%25BE%25E7%25A4%25BA%25E7%259B%25AE%25E5%25BD%2595&rsv_pq=a101777c002b439b&rsv_t=960en56ZL9y8HXsyO1N7algGrZBOdtpeO8No%2FP8V8i7cnbVO7OD8KwciQ8wPcggPFjZJ&rqlang=cn&rsv_enter=1&rsv_dl=tb&rsv_btype=t&inputT=2153&rsv_sug3=83&rsv_sug1=47&rsv_sug7=100&rsv_sug2=0&rsv_sug4=2153");
        mediaDTO.setUid("XXX");
        mediaDTO.setK3_id(1133);
        mediaDTO.setK3IdName("XXX");
        mediaDTO.setDomainName("XXX");
        mediaDTO.setName("XXX");
        mediaDTO.setIndexId("XXXId");
        mediaDTO.setFollowersCount(10);
        mediaDTO.setArea("XX");
        mediaDTO.setAvatar("");
        mediaDTO.setMediaInfoTag(0);
        mediaDTO.setNewCode("XXX");
        mediaDTO.setNewCodeName("XXX");
        mediaDTO.setMediaLevel("XXX");
        mediaDTO.setIndustry("XXX");
        mediaDTO.setVerifyType("XXX");
        mediaDTO.setWeiboVerifyType("XXX");
        mediaDTO.setBigVLabel(1);
        mediaDTO.setIpAddressManual("XXX");

        // 创建WeMediaInformationSourceBean
        WeMediaInformationSourceBean bean = new WeMediaInformationSourceBean();
        bean.setServiceUrl("http://your-api-server/api/wemedia/save");
        
        // 根据媒体类型添加到对应列表
        if ("douyin".equals(mediaDTO.getMediaTag())) {
            bean.setOtherMediaList(Arrays.asList(mediaDTO));
        }

        System.out.println("输入的单个媒体对象:");
        System.out.println(JSON.toJSONString(mediaDTO, true));

        // 模拟构建的请求体（这是weMediaSaveOrUpdate方法会构建的）
        JSONObject expectedRequestBody = new JSONObject();
        expectedRequestBody.put("params", Arrays.asList(mediaDTO));
        expectedRequestBody.put("refreshPolicy", 1);

        System.out.println("\n期望的请求体格式:");
        System.out.println(JSON.toJSONString(expectedRequestBody, true));
    }

    /**
     * 示例2：多个媒体对象的保存
     */
    public void multipleMediaExample() {
        // 创建微信媒体对象
        MediaBaseDTO wechatMedia = new MediaBaseDTO();
        wechatMedia.setName("微信公众号");
        wechatMedia.setSourceType("微信");
        wechatMedia.setMediaTag("wechat");
        wechatMedia.setUid("wechat_123");

        // 创建微博媒体对象
        MediaBaseDTO weiboMedia = new MediaBaseDTO();
        weiboMedia.setName("微博账号");
        weiboMedia.setSourceType("微博");
        weiboMedia.setMediaTag("weibo");
        weiboMedia.setUid("weibo_456");

        // 创建抖音媒体对象
        MediaBaseDTO douyinMedia = new MediaBaseDTO();
        douyinMedia.setName("抖音账号");
        douyinMedia.setSourceType("其他");
        douyinMedia.setMediaTag("douyin");
        douyinMedia.setUid("douyin_789");

        // 创建WeMediaInformationSourceBean
        WeMediaInformationSourceBean bean = new WeMediaInformationSourceBean();
        bean.setServiceUrl("http://your-api-server/api/wemedia/save");
        
        // 分别添加到对应列表
        bean.setWechatMeidaList(Arrays.asList(wechatMedia));
        bean.setWeiboMeidaList(Arrays.asList(weiboMedia));
        bean.setOtherMediaList(Arrays.asList(douyinMedia));

        System.out.println("输入的多个媒体对象:");
        System.out.println("微信: " + JSON.toJSONString(wechatMedia));
        System.out.println("微博: " + JSON.toJSONString(weiboMedia));
        System.out.println("抖音: " + JSON.toJSONString(douyinMedia));

        // 模拟构建的请求体
        JSONObject expectedRequestBody = new JSONObject();
        expectedRequestBody.put("params", Arrays.asList(wechatMedia, weiboMedia, douyinMedia));
        expectedRequestBody.put("refreshPolicy", 1);

        System.out.println("\n期望的请求体格式:");
        System.out.println(JSON.toJSONString(expectedRequestBody, true));
    }

    /**
     * 示例3：验证请求体结构
     */
    public void validateRequestBodyStructure() {
        System.out.println("=== 请求体结构验证 ===");
        
        // 你的输入格式
        String inputJson = "{\n" +
                "    \"accountIndexName\": \"XX\",\n" +
                "    \"mediaTag\": \"douyin\",\n" +
                "    \"code\": \"XXX\",\n" +
                "    \"homeUrl\": \"XXX\",\n" +
                "    \"uid\": \"XXX\",\n" +
                "    \"k3Id\": \"1133\",\n" +
                "    \"k3IdName\": \"XXX\",\n" +
                "    \"domainName\": \"XXX\",\n" +
                "    \"name\": \"XXX\",\n" +
                "    \"indexId\": \"XXXId\",\n" +
                "    \"followersCount\": 10,\n" +
                "    \"area\": \"XX\",\n" +
                "    \"avatar\": \"\",\n" +
                "    \"mediaInfoTag\": 0,\n" +
                "    \"newCode\": \"XXX\",\n" +
                "    \"newCodeName\": \"XXX\",\n" +
                "    \"mediaLevel\": \"XXX\",\n" +
                "    \"industry\": \"XXX\",\n" +
                "    \"verifyType\": \"XXX\",\n" +
                "    \"weiboVerifyType\": \"XXX\",\n" +
                "    \"bigVLabel\": 1,\n" +
                "    \"ipAddressManual\": \"XXX\"\n" +
                "}";

        // 期望的输出格式
        String outputJson = "{\n" +
                "    \"params\": [{\n" +
                "        \"accountIndexName\": \"XX\",\n" +
                "        \"mediaTag\": \"douyin\",\n" +
                "        \"code\": \"XXX\",\n" +
                "        \"homeUrl\": \"XXX\",\n" +
                "        \"uid\": \"XXX\",\n" +
                "        \"k3Id\": \"1133\",\n" +
                "        \"k3IdName\": \"XXX\",\n" +
                "        \"domainName\": \"XXX\",\n" +
                "        \"name\": \"XXX\",\n" +
                "        \"indexId\": \"XXXId\",\n" +
                "        \"followersCount\": 10,\n" +
                "        \"area\": \"XX\",\n" +
                "        \"avatar\": \"\",\n" +
                "        \"mediaInfoTag\": 0,\n" +
                "        \"newCode\": \"XXX\",\n" +
                "        \"newCodeName\": \"XXX\",\n" +
                "        \"mediaLevel\": \"XXX\",\n" +
                "        \"industry\": \"XXX\",\n" +
                "        \"verifyType\": \"XXX\",\n" +
                "        \"weiboVerifyType\": \"XXX\",\n" +
                "        \"bigVLabel\": 1,\n" +
                "        \"ipAddressManual\": \"XXX\"\n" +
                "    }],\n" +
                "    \"refreshPolicy\": 1\n" +
                "}";

        System.out.println("输入格式（单个对象）:");
        System.out.println(inputJson);
        
        System.out.println("\n输出格式（包装成数组）:");
        System.out.println(outputJson);
        
        System.out.println("\n转换说明:");
        System.out.println("1. 将单个媒体对象包装到 params 数组中");
        System.out.println("2. 添加 refreshPolicy 字段，值为 1");
        System.out.println("3. 发送POST请求到外部API");
    }

    public static void main(String[] args) {
        WeMediaSaveOrUpdateExample example = new WeMediaSaveOrUpdateExample();
        
        System.out.println("=== WeMedia保存或更新示例 ===\n");
        
        example.singleMediaExample();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        example.multipleMediaExample();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        example.validateRequestBodyStructure();
    }
}
