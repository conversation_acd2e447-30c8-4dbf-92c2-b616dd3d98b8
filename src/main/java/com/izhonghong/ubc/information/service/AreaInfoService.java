package com.izhonghong.ubc.information.service;

import com.izhonghong.ubc.information.entity.AreaInfo;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.izhonghong.ubc.information.entity.vo.AreaInfoVo;

/**
 * <AUTHOR>
 * @since 2022-03-18
 */
public interface AreaInfoService extends IService<AreaInfo> {

	List<AreaInfo> selectAreaInfoList(String root,Integer type,String name);

	JSONObject selectAreaCode(String area);
	
	boolean checkAreaName(String name);

    List<AreaInfoVo> selectInlandTree();

    List<AreaInfoVo> selectInlandTreeNew();

}
