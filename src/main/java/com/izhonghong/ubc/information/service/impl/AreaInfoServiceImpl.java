package com.izhonghong.ubc.information.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izhonghong.ubc.information.constant.CommonConstant;
import com.izhonghong.ubc.information.constant.DataSourceConstant;
import com.izhonghong.ubc.information.dao.AreaInfoDao;
import com.izhonghong.ubc.information.entity.AreaInfo;
import com.izhonghong.ubc.information.entity.vo.AreaInfoVo;
import com.izhonghong.ubc.information.service.AreaInfoService;
import com.izhonghong.ubc.information.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-03-18
 */
@Service
@DS(DataSourceConstant.DATA_SOURCE)
public class AreaInfoServiceImpl extends ServiceImpl<AreaInfoDao, AreaInfo> implements AreaInfoService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public List<AreaInfo> selectAreaInfoList(String root, Integer type, String name) {
        List<AreaInfo> list = new ArrayList<>();
        if (!StringUtils.isEmpty(name)) {
            return selectAreaByName(name, list);
        }
        QueryWrapper<AreaInfo> query = new QueryWrapper<>();
        query.lambda().eq(AreaInfo::getParentCode, root).eq(!StringUtils.isEmpty(type), AreaInfo::getTypes, type);
        list = this.list(query);
        return list;
    }

    public List<AreaInfo> selectAreaByName(String name, List<AreaInfo> list) {
        QueryWrapper<AreaInfo> query = new QueryWrapper<>();
        query.lambda().like(AreaInfo::getName, name);
        List<AreaInfo> relist = this.list(query);
        if (!StringUtil.isListNull(relist)) {
            list.addAll(relist);
            List<String> parentCode = relist.stream().filter(t -> !StringUtils.isEmpty(t.getParentCode())).map(AreaInfo::getParentCode).collect(Collectors.toList());
            selectAreaByIds(parentCode, list);
        }
        list = list.stream().distinct().collect(Collectors.toList());
        return list;
    }

    public void selectAreaByIds(List<String> parentCode, List<AreaInfo> list) {
        QueryWrapper<AreaInfo> query = new QueryWrapper<>();
        query.lambda().in(AreaInfo::getCode, parentCode);
        List<AreaInfo> relist = this.list(query);
        if (!StringUtil.isListNull(relist)) {
            list.addAll(relist);
            List<String> parentCodes = relist.stream().filter(t -> !StringUtils.isEmpty(t.getParentCode())).map(AreaInfo::getParentCode).collect(Collectors.toList());
            selectAreaByIds(parentCodes, list);
        }
	}


    @Override
    public JSONObject selectAreaCode(String area) {
        JSONObject resultJson = new JSONObject();
        area = area.replace("境内", "").replace("境外", "");
        List<String> areaArray = StringUtil.stringToListStr(area, ".");
        String[] codeArray = new String[areaArray.size() + 1];
        String[] nameArray = new String[areaArray.size() + 1];
        for (int i = 0; i < areaArray.size(); i++) {
            getcode(areaArray.get(i), i, codeArray, nameArray);
        }
        resultJson.put("names", nameArray);
        resultJson.put("codes", codeArray);

        return resultJson;
    }

    private void getcode(String area, Integer level, String[] codeArray, String[] nameArray) {
        int lev = level + 1;
        List<AreaInfo> list = selectRightName(area, lev);
        if (list != null && list.size() > 0) {
            AreaInfo areaInfo = list.get(0);
            codeArray[lev] = areaInfo.getCode();
            nameArray[lev] = areaInfo.getName();
            if (areaInfo.getTypes() < 1) {
                codeArray[0] = "0";
                nameArray[0] = "境内";
            } else {
                codeArray[0] = "10";
                nameArray[0] = "境外";
            }
        }
    }


    private List<AreaInfo> selectRightName(String area, Integer level) {
        QueryWrapper<AreaInfo> query = new QueryWrapper<>();
        query.lambda().eq(AreaInfo::getLevel, level).likeRight(AreaInfo::getName, area);
        List<AreaInfo> list = this.list(query);
        return list;
    }

    @Override
    public boolean checkAreaName(String name) {
        boolean flage = true;
        name = name.replace("境内", "").replace("境外", "");
        List<String> areaArray = StringUtil.stringToListStr(name, ".");
        for (int i = 0; i < areaArray.size(); i++) {
            String area = areaArray.get(i);
            List<AreaInfo> list = selectRightName(area, i + 1);
            if (list == null || list.size() < 1) {
                flage = false;
                break;
            }
        }
        return flage;
    }

    @Override
    public List<AreaInfoVo> selectInlandTree() {
        Object object = redisTemplate.opsForValue().get(CommonConstant.INLAND_AREA_TREE_KEY);
        if (object != null) {
            return JSON.parseArray(object.toString(), AreaInfoVo.class);
        }
        QueryWrapper<AreaInfo> query = new QueryWrapper<>();
        query.eq("types", 0).ne("level", 4).ne("code", 0);
        List<AreaInfo> allList = this.list(query);
        List<AreaInfo> parentList = allList.stream().filter(a -> "0".equals(a.getParentCode())).collect(Collectors.toList());
        allList.removeAll(parentList);
        selectAreaByParentCode(parentList, allList);
        List<AreaInfoVo> result = new LinkedList<>();
        if (CollUtil.isNotEmpty(parentList)) {
            result = convertToVO(parentList);
            redisTemplate.opsForValue().set(CommonConstant.INLAND_AREA_TREE_KEY, JSON.toJSONString(result), 30, TimeUnit.DAYS);
        }
        return result;
    }

    private void selectAreaByParentCode(List<AreaInfo> parentList, List<AreaInfo> list) {
        for (AreaInfo parent : parentList) {
            List<AreaInfo> children = list.stream().filter(a -> parent.getCode().equals(a.getParentCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(children)) {
                parent.setChildren(children);
                list.removeAll(children);
                selectAreaByParentCode(children, list);
            }
        }
    }

    private List<AreaInfoVo> convertToVO(List<AreaInfo> parentList) {
        List<AreaInfoVo> result = new LinkedList<>();
        for (AreaInfo areaInfo : parentList) {
            AreaInfoVo vo = new AreaInfoVo();
            vo.setId(areaInfo.getId());
            vo.setName(areaInfo.getName());
            if (CollUtil.isNotEmpty(areaInfo.getChildren())) {
                List<AreaInfoVo> children = convertToVO(areaInfo.getChildren());
                vo.setChildren(children);
            }
            result.add(vo);
        }
        return result;
    }

}
