package com.izhonghong.ubc.information.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.service.impl.abstracts.WebSpiderInformationSourceDataServiceImpl;
import com.izhonghong.ubc.information.util.components.ISearchDataAccess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

/**
 * WeMedia保存或更新功能测试
 */
@ExtendWith(MockitoExtension.class)
public class WeMediaSaveOrUpdateTest {

    private WebSpiderInformationSourceDataServiceImpl service;
    private WeMediaInformationSourceBean weMediaBean;

    @BeforeEach
    void setUp() {
        service = new WebSpiderInformationSourceDataServiceImpl();
        weMediaBean = new WeMediaInformationSourceBean();
        weMediaBean.setServiceUrl("http://test-api-server/api/wemedia/save");
    }

    @Test
    void testSingleMediaSaveOrUpdate() throws Exception {
        // 创建单个媒体对象
        MediaBaseDTO mediaDTO = createTestMediaDTO();
        
        // 设置到其他媒体列表（抖音）
        weMediaBean.setOtherMediaList(Arrays.asList(mediaDTO));

        // Mock ISearchDataAccess.postJson
        try (MockedStatic<ISearchDataAccess> mockedStatic = mockStatic(ISearchDataAccess.class)) {
            JSONObject mockResponse = new JSONObject();
            mockResponse.put("success", true);
            mockResponse.put("message", "保存成功");
            
            mockedStatic.when(() -> ISearchDataAccess.postJson(anyString(), any(JSONObject.class)))
                    .thenReturn(mockResponse);

            // 调用方法
            JSONObject result = service.weMediaSaveOrUpdate(weMediaBean);

            // 验证结果
            assertNotNull(result);
            assertEquals(true, result.getBoolean("success"));
            assertEquals("保存成功", result.getString("message"));

            // 验证调用参数（通过捕获参数来验证）
            mockedStatic.verify(() -> ISearchDataAccess.postJson(anyString(), any(JSONObject.class)));
        }
    }

    @Test
    void testMultipleMediaSaveOrUpdate() throws Exception {
        // 创建多个媒体对象
        MediaBaseDTO wechatMedia = createTestMediaDTO();
        wechatMedia.setSourceType("微信");
        wechatMedia.setMediaTag("wechat");
        wechatMedia.setName("微信公众号");

        MediaBaseDTO weiboMedia = createTestMediaDTO();
        weiboMedia.setSourceType("微博");
        weiboMedia.setMediaTag("weibo");
        weiboMedia.setName("微博账号");

        MediaBaseDTO douyinMedia = createTestMediaDTO();
        douyinMedia.setSourceType("其他");
        douyinMedia.setMediaTag("douyin");
        douyinMedia.setName("抖音账号");

        // 设置到对应列表
        weMediaBean.setWechatMeidaList(Arrays.asList(wechatMedia));
        weMediaBean.setWeiboMeidaList(Arrays.asList(weiboMedia));
        weMediaBean.setOtherMediaList(Arrays.asList(douyinMedia));

        // Mock ISearchDataAccess.postJson
        try (MockedStatic<ISearchDataAccess> mockedStatic = mockStatic(ISearchDataAccess.class)) {
            JSONObject mockResponse = new JSONObject();
            mockResponse.put("success", true);
            mockResponse.put("count", 3);
            
            mockedStatic.when(() -> ISearchDataAccess.postJson(anyString(), any(JSONObject.class)))
                    .thenReturn(mockResponse);

            // 调用方法
            JSONObject result = service.weMediaSaveOrUpdate(weMediaBean);

            // 验证结果
            assertNotNull(result);
            assertEquals(true, result.getBoolean("success"));
            assertEquals(3, result.getIntValue("count"));
        }
    }

    @Test
    void testRequestBodyStructure() throws Exception {
        // 创建测试媒体对象
        MediaBaseDTO mediaDTO = createTestMediaDTO();
        weMediaBean.setOtherMediaList(Arrays.asList(mediaDTO));

        // Mock ISearchDataAccess.postJson 并捕获参数
        try (MockedStatic<ISearchDataAccess> mockedStatic = mockStatic(ISearchDataAccess.class)) {
            JSONObject mockResponse = new JSONObject();
            mockResponse.put("success", true);
            
            // 使用Answer来捕获和验证参数
            mockedStatic.when(() -> ISearchDataAccess.postJson(anyString(), any(JSONObject.class)))
                    .thenAnswer(invocation -> {
                        String url = invocation.getArgument(0);
                        JSONObject requestBody = invocation.getArgument(1);
                        
                        // 验证URL
                        assertEquals("http://test-api-server/api/wemedia/save", url);
                        
                        // 验证请求体结构
                        assertNotNull(requestBody);
                        assertTrue(requestBody.containsKey("params"));
                        assertTrue(requestBody.containsKey("refreshPolicy"));
                        assertEquals(1, requestBody.getIntValue("refreshPolicy"));
                        
                        // 验证params是数组
                        Object params = requestBody.get("params");
                        assertTrue(params instanceof JSONArray);
                        
                        JSONArray paramsArray = (JSONArray) params;
                        assertEquals(1, paramsArray.size());
                        
                        // 验证数组中的对象
                        JSONObject mediaObject = paramsArray.getJSONObject(0);
                        assertNotNull(mediaObject);
                        assertEquals("douyin", mediaObject.getString("mediaTag"));
                        assertEquals("测试账号", mediaObject.getString("name"));
                        assertEquals("XXX", mediaObject.getString("uid"));
                        
                        return mockResponse;
                    });

            // 调用方法
            JSONObject result = service.weMediaSaveOrUpdate(weMediaBean);

            // 验证结果
            assertNotNull(result);
            assertEquals(true, result.getBoolean("success"));
        }
    }

    @Test
    void testEmptyMediaLists() throws Exception {
        // 不设置任何媒体列表
        
        // Mock ISearchDataAccess.postJson
        try (MockedStatic<ISearchDataAccess> mockedStatic = mockStatic(ISearchDataAccess.class)) {
            JSONObject mockResponse = new JSONObject();
            mockResponse.put("success", true);
            mockResponse.put("count", 0);
            
            mockedStatic.when(() -> ISearchDataAccess.postJson(anyString(), any(JSONObject.class)))
                    .thenAnswer(invocation -> {
                        JSONObject requestBody = invocation.getArgument(1);
                        
                        // 验证空数组
                        JSONArray paramsArray = requestBody.getJSONArray("params");
                        assertNotNull(paramsArray);
                        assertEquals(0, paramsArray.size());
                        
                        return mockResponse;
                    });

            // 调用方法
            JSONObject result = service.weMediaSaveOrUpdate(weMediaBean);

            // 验证结果
            assertNotNull(result);
            assertEquals(true, result.getBoolean("success"));
        }
    }

    @Test
    void testNullServiceUrl() {
        // 设置空的serviceUrl
        weMediaBean.setServiceUrl(null);
        
        // 应该抛出异常
        assertThrows(Exception.class, () -> {
            service.weMediaSaveOrUpdate(weMediaBean);
        });
    }

    @Test
    void testEmptyServiceUrl() {
        // 设置空字符串serviceUrl
        weMediaBean.setServiceUrl("");
        
        // 应该抛出异常
        assertThrows(Exception.class, () -> {
            service.weMediaSaveOrUpdate(weMediaBean);
        });
    }

    /**
     * 创建测试用的MediaBaseDTO对象
     */
    private MediaBaseDTO createTestMediaDTO() {
        MediaBaseDTO mediaDTO = new MediaBaseDTO();
        mediaDTO.setAccountIndexName("XX");
        mediaDTO.setMediaTag("douyin");
        mediaDTO.setCode("XXX");
        mediaDTO.setHomeUrl("https://www.example.com");
        mediaDTO.setUid("XXX");
        mediaDTO.setK3_id(1133);
        mediaDTO.setK3IdName("XXX");
        mediaDTO.setDomainName("XXX");
        mediaDTO.setName("测试账号");
        mediaDTO.setIndexId("XXXId");
        mediaDTO.setFollowersCount(10);
        mediaDTO.setArea("XX");
        mediaDTO.setAvatar("");
        mediaDTO.setMediaInfoTag(0);
        mediaDTO.setNewCode("XXX");
        mediaDTO.setNewCodeName("XXX");
        mediaDTO.setMediaLevel("XXX");
        mediaDTO.setIndustry("XXX");
        mediaDTO.setVerifyType("XXX");
        mediaDTO.setWeiboVerifyType("XXX");
        mediaDTO.setBigVLabel(1);
        mediaDTO.setIpAddressManual("XXX");
        return mediaDTO;
    }
}
